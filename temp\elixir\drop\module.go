package drop

import (
	"encoding/json"
	"fmt"
	"jackpot/api/core"
	"jackpot/filelog"
	"jackpot/items"
	"jackpot/net"
	"jackpot/users"
	"os"
)

// Request 请求体
type Request struct {
	UserId string `json:"uid"`
	Cost   int64  `json:"cost"`
	Repeat int64  `json:"repeat"`
	Jy     int64  `json:"jy"` // 精英数量
}

// Response 响应数据
type Response struct {
	Request
	Items []items.Item `json:"items"`
}

// Handler 处理器
type Handler struct {
	logger  core.Logger
	itemMap map[int64][]items.Item
}

// NewHandler 创建处理器
func NewHandler() *Handler {
	return &Handler{
		itemMap: make(map[int64][]items.Item),
	}
}

// Init 初始化处理器
func (h *Handler) Init(logger *filelog.FileLogger, config any) error {
	h.logger = core.NewLoggerAdapter(logger)

	// 读取文件内容
	jsonData, err := os.ReadFile("elixir_drop.json")
	if err != nil {
		return err
	}

	var itemMapTemp = make(map[string][]items.ItemConfig)
	err = json.Unmarshal(jsonData, &itemMapTemp)
	if err != nil {
		return err
	}

	for k, v := range itemMapTemp {
		var key int64
		fmt.Sscanf(k, "%d", &key)
		var itemList []items.Item
		for _, itemConfig := range v {
			itemList = append(itemList, items.Item(itemConfig))
		}
		h.itemMap[key] = itemList
	}

	h.logger.Debug("elixir_drop", "itemMap: %+v", h.itemMap)
	return nil
}

// Validate 验证请求
func (h *Handler) Validate(req Request) error {
	// 检查UID参数
	if req.UserId == "" {
		return fmt.Errorf("missing uid parameter")
	}

	// 检查Cost参数
	switch req.Cost {
	case net.COST_TYPE_100, net.COST_TYPE_1000, net.COST_TYPE_10000:
	default:
		return fmt.Errorf("invalid cost parameter")
	}

	// 检查Repeat参数
	if req.Repeat <= 0 {
		return fmt.Errorf("invalid or missing repeat parameter")
	}

	// 检查JY参数
	if req.Jy < 0 {
		return fmt.Errorf("invalid jy parameter")
	}

	return nil
}

// Handle 处理请求
func (h *Handler) Handle(req Request) (int64, net.Response, error) {
	jackpot, items := h.open(req.UserId, req.Cost, req.Repeat, req.Jy)

	response := net.Response{
		Code: 0,
		Msg:  "",
		Data: &Response{
			Request: req,
			Items:   items,
		},
	}

	return jackpot, response, nil
}

// open 开奖逻辑
func (h *Handler) open(uid string, cost int64, repeat int64, jy int64) (int64, []items.Item) {
	allCost := cost * repeat

	global := users.GetGlobalStats(cost, users.VER_ELIXIR)
	global.TotalCost += allCost

	// 计算奖池 = 总成本 * RTP - 世界BOSS抽水
	jackpot := int64(float64(allCost) * (global.Rtp))
	global.TotalJackpot += jackpot

	// 把内丹奖池填充到总奖池中
	if global.NeidanJackpot >= 0 {
		global.TotalJackpot += global.NeidanJackpot
		global.NeidanJackpot = 0
	}

	h.logger.Debug(uid, "[0] cost:%d, jackpot:%d, global jackpot:%d, neidan jackpot:%d", allCost, jackpot, global.TotalJackpot, global.NeidanJackpot)

	// 选择物品列表
	var selectedItemMap map[string]*items.Item = make(map[string]*items.Item)
	for range int(repeat) {
		selectedItem := h.selectItem(uid, cost)
		selectedItem.Total = 1

		if jy > 0 {
			jy -= 1
			selectedItem.Total = 2
		}

		if mapItem, exists := selectedItemMap[selectedItem.Name]; exists {
			mapItem.Total += selectedItem.Total
		} else {
			selectedItemMap[selectedItem.Name] = &selectedItem
		}
	}

	var selectedItems []items.Item
	for _, v := range selectedItemMap {
		selectedItems = append(selectedItems, *v)
		h.logger.Debug(uid, "[10] return item:%s, value:%d, count:%d, total:%d", v.Name, v.Value, v.Count, v.Total)
	}

	h.logger.Debug(uid, "[10] global jackpot:%d", global.TotalJackpot)

	users.UpdateGobalStats(global)

	return global.TotalJackpot, selectedItems
}

// selectItem 选择物品
func (h *Handler) selectItem(uid string, cost int64) items.Item {
	return items.SelectItem(uid, h.itemMap[cost])
}

// NewModule 创建模块
func NewModule() core.APIModule {
	handler := NewHandler()

	return core.NewModuleBuilder[Request, net.Response]("elixir_drop", "v1", net.MSG_TYPE_ELIXIR_DROP).
		WithHandler(handler).
		Build()
}
